{"name": "ai-augment", "displayName": "AI Augment", "description": "AI-powered code augmentation", "version": "0.0.1", "engines": {"vscode": "^1.75.0"}, "categories": ["Programming Languages", "Other"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "ai-augment.explain", "title": "AI: Explain Code"}, {"command": "ai-augment.refactor", "title": "AI: Refactor Code"}, {"command": "ai-augment.generate", "title": "AI: Generate Code"}, {"command": "ai-augment.document", "title": "AI: Document Code"}], "menus": {"editor/context": [{"command": "ai-augment.explain", "group": "ai@1", "when": "editorHasSelection"}, {"command": "ai-augment.refactor", "group": "ai@2", "when": "editorHasSelection"}, {"command": "ai-augment.document", "group": "ai@3", "when": "editorHasSelection"}], "commandPalette": [{"command": "ai-augment.generate", "when": "editorTextFocus"}]}}, "scripts": {"vscode:prepublish": "npm run package", "compile": "webpack", "watch": "webpack --watch", "package": "webpack --mode production --devtool hidden-source-map", "pretest": "npm run compile-tests && npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.75.0", "@types/glob": "^8.0.0", "@types/mocha": "^10.0.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.42.0", "@typescript-eslint/parser": "^5.42.0", "eslint": "^8.26.0", "glob": "^8.0.3", "mocha": "^10.1.0", "typescript": "^4.8.4", "ts-loader": "^9.4.1", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "@vscode/test-electron": "^2.2.0"}, "dependencies": {"axios": "^1.3.4", "vscode-uri": "^3.0.7"}}