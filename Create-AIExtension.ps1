# Create AI Augment VS Code Extension Project
Write-Host "Creating AI Augment VS Code Extension..." -ForegroundColor Cyan

$projectRoot = "vscode-ai-augment"
New-Item -Path $projectRoot -ItemType Directory -Force | Out-Null
Set-Location $projectRoot

# Create directory structure
New-Item -Path "src" -ItemType Directory -Force | Out-Null
New-Item -Path ".vscode" -ItemType Directory -Force | Out-Null

# Create fixed extension.ts
$extensionContent = @'
import * as vscode from 'vscode';
import axios from 'axios';

const OLLAMA_HOST = 'http://localhost:11434';

export function activate(context: vscode.ExtensionContext) {
    console.log('AI Augment extension activated');

    // Register commands
    const explainCommand = vscode.commands.registerCommand('ai-augment.explain', explainCode);
    const refactorCommand = vscode.commands.registerCommand('ai-augment.refactor', refactorCode);
    const generateCommand = vscode.commands.registerCommand('ai-augment.generate', generateCode);
    const documentCommand = vscode.commands.registerCommand('ai-augment.document', documentCode);

    context.subscriptions.push(explainCommand, refactorCommand, generateCommand, documentCommand);
}

async function callOllama(prompt: string, model = 'llama3'): Promise<string> {
    try {
        const response = await axios.post(`${OLLAMA_HOST}/api/generate`, {
            model,
            prompt,
            stream: false
        });

        return response.data.response;
    } catch (error) {
        console.error('Error calling Ollama:', error);
        throw error;
    }
}

async function explainCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const prompt = `Explain the following code in simple terms:\n\n${selectedText}\n\nExplanation:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is analyzing your code...",
        cancellable: false
    }, async () => {
        try {
            const explanation = await callOllama(prompt);
            const panel = vscode.window.createWebviewPanel(
                'codeExplanation',
                'Code Explanation',
                vscode.ViewColumn.Beside,
                {}
            );
            panel.webview.html = getWebviewContent(explanation);
        } catch (error) {
            vscode.window.showErrorMessage('Failed to get explanation');
        }
    });
}

async function refactorCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const prompt = `Refactor the following code to make it more efficient and readable:\n\n${selectedText}\n\nRefactored code:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is refactoring your code...",
        cancellable: false
    }, async () => {
        try {
            const refactoredCode = await callOllama(prompt);
            editor.edit(editBuilder => {
                editBuilder.replace(selection, refactoredCode);
            });
        } catch (error) {
            vscode.window.showErrorMessage('Failed to refactor code');
        }
    });
}

async function generateCode() {
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe the code you want to generate',
        placeHolder: 'e.g., A Python function to calculate factorial'
    });

    if (!prompt) {
        return;
    }

    const fullPrompt = `Generate code based on the following description:\n\n${prompt}\n\nCode:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is generating code...",
        cancellable: false
    }, async () => {
        try {
            const generatedCode = await callOllama(fullPrompt);
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                editor.edit(editBuilder => {
                    editBuilder.insert(editor.selection.active, generatedCode);
                });
            } else {
                const doc = await vscode.workspace.openTextDocument({
                    content: generatedCode,
                    language: 'python' // Default language
                });
                vscode.window.showTextDocument(doc);
            }
        } catch (error) {
            vscode.window.showErrorMessage('Failed to generate code');
        }
    });
}

async function documentCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const prompt = `Generate documentation for the following code:\n\n${selectedText}\n\nDocumentation:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is documenting your code...",
        cancellable: false
    }, async () => {
        try {
            const documentation = await callOllama(prompt);
            editor.edit(editBuilder => {
                // Insert documentation above the selected code
                const position = new vscode.Position(selection.start.line, 0);
                editBuilder.insert(position, documentation + '\n\n');
            });
        } catch (error) {
            vscode.window.showErrorMessage('Failed to generate documentation');
        }
    });
}

function getWebviewContent(text: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Explanation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div>${text.replace(/\n/g, '<br>')}</div>
</body>
</html>`;
}

export function deactivate() {}
'@
Set-Content -Path "src\extension.ts" -Value $extensionContent

# Create package.json
$packageJson = @'
{
    "name": "ai-augment",
    "displayName": "AI Augment",
    "description": "AI-powered code augmentation",
    "version": "0.0.1",
    "engines": {
        "vscode": "^1.75.0"
    },
    "categories": [
        "Programming Languages",
        "Other"
    ],
    "main": "./dist/extension.js",
    "contributes": {
        "commands": [
            {
                "command": "ai-augment.explain",
                "title": "AI: Explain Code"
            },
            {
                "command": "ai-augment.refactor",
                "title": "AI: Refactor Code"
            },
            {
                "command": "ai-augment.generate",
                "title": "AI: Generate Code"
            },
            {
                "command": "ai-augment.document",
                "title": "AI: Document Code"
            }
        ],
        "menus": {
            "editor/context": [
                {
                    "command": "ai-augment.explain",
                    "group": "ai@1",
                    "when": "editorHasSelection"
                },
                {
                    "command": "ai-augment.refactor",
                    "group": "ai@2",
                    "when": "editorHasSelection"
                },
                {
                    "command": "ai-augment.document",
                    "group": "ai@3",
                    "when": "editorHasSelection"
                }
            ],
            "commandPalette": [
                {
                    "command": "ai-augment.generate",
                    "when": "editorTextFocus"
                }
            ]
        }
    },
    "scripts": {
        "vscode:prepublish": "npm run package",
        "compile": "webpack",
        "watch": "webpack --watch",
        "package": "webpack --mode production --devtool hidden-source-map",
        "pretest": "npm run compile-tests && npm run compile && npm run lint",
        "lint": "eslint src --ext ts",
        "test": "node ./out/test/runTest.js"
    },
    "devDependencies": {
        "@types/vscode": "^1.75.0",
        "@types/glob": "^8.0.0",
        "@types/mocha": "^10.0.0",
        "@types/node": "16.x",
        "@typescript-eslint/eslint-plugin": "^5.42.0",
        "@typescript-eslint/parser": "^5.42.0",
        "eslint": "^8.26.0",
        "glob": "^8.0.3",
        "mocha": "^10.1.0",
        "typescript": "^4.8.4",
        "ts-loader": "^9.4.1",
        "webpack": "^5.75.0",
        "webpack-cli": "^5.0.1",
        "@vscode/test-electron": "^2.2.0"
    },
    "dependencies": {
        "axios": "^1.3.4",
        "vscode-uri": "^3.0.7"
    }
}
'@
Set-Content -Path "package.json" -Value $packageJson

# Create webpack.config.js
$webpackConfig = @'
const path = require('path');

module.exports = {
    target: 'node',
    entry: './src/extension.ts',
    output: {
        path: path.resolve(__dirname, 'dist'),
        filename: 'extension.js',
        libraryTarget: 'commonjs2',
        devtoolModuleFilenameTemplate: '../[resource-path]'
    },
    devtool: 'source-map',
    externals: {
        vscode: 'commonjs vscode'
    },
    resolve: {
        extensions: ['.ts', '.js']
    },
    module: {
        rules: [
            {
                test: /\.ts$/,
                exclude: /node_modules/,
                use: [
                    {
                        loader: 'ts-loader'
                    }
                ]
            }
        ]
    }
};
'@
Set-Content -Path "webpack.config.js" -Value $webpackConfig

# Create tsconfig.json
$tsconfig = @'
{
    "compilerOptions": {
        "module": "commonjs",
        "target": "es6",
        "outDir": "out",
        "lib": [
            "es6"
        ],
        "sourceMap": true,
        "rootDir": "src",
        "strict": true,
        "esModuleInterop": true,
        "skipLibCheck": true
    },
    "exclude": [
        "node_modules"
    ]
}
'@
Set-Content -Path "tsconfig.json" -Value $tsconfig

# Create launch.json
$launchJson = @'
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Run Extension",
            "type": "extensionHost",
            "request": "launch",
            "args": [
                "--extensionDevelopmentPath=${workspaceFolder}"
            ],
            "outFiles": [
                "${workspaceFolder}/dist/**/*.js"
            ],
            "preLaunchTask": "npm: watch"
        }
    ]
}
'@
Set-Content -Path ".vscode\launch.json" -Value $launchJson

# Create tasks.json
$tasksJson = @'
{
    "version": "2.0.0",
    "tasks": [
        {
            "type": "npm",
            "script": "watch",
            "group": "build",
            "isBackground": true,
            "problemMatcher": "$tsc-watch",
            "presentation": {
                "reveal": "never"
            }
        }
    ]
}
'@
Set-Content -Path ".vscode\tasks.json" -Value $tasksJson

# Create .gitignore
$gitignore = @'
node_modules
dist
out
*.vsix
.DS_Store
'@
Set-Content -Path ".gitignore" -Value $gitignore

# Initialize npm and install dependencies
Write-Host "`nInstalling dependencies..." -ForegroundColor Yellow
npm install

# Build the extension
Write-Host "`nBuilding extension..." -ForegroundColor Yellow
npm run compile

# Open in VS Code
Write-Host "`nProject setup complete!" -ForegroundColor Green
Write-Host "Opening project in VS Code..." -ForegroundColor Cyan
code .

Write-Host "`nUse F5 to run the extension in a new VS Code window" -ForegroundColor Yellow
Write-Host "Make sure Ollama is running at http://localhost:11434" -ForegroundColor Yellow