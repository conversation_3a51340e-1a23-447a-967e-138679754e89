import * as vscode from 'vscode';
import axios from 'axios';

const OLLAMA_HOST = 'http://localhost:11434';
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

interface AIProvider {
    name: string;
    available: boolean;
}

export function activate(context: vscode.ExtensionContext) {
    console.log('AI Augment extension activated');

    // Register commands
    const explainCommand = vscode.commands.registerCommand('ai-augment.explain', explainCode);
    const refactorCommand = vscode.commands.registerCommand('ai-augment.refactor', refactorCode);
    const generateCommand = vscode.commands.registerCommand('ai-augment.generate', generateCode);
    const documentCommand = vscode.commands.registerCommand('ai-augment.document', documentCode);
    const selectModelCommand = vscode.commands.registerCommand('ai-augment.selectModel', selectModel);

    context.subscriptions.push(explainCommand, refactorCommand, generateCommand, documentCommand, selectModelCommand);
}

async function selectModel() {
    const providers = await getAvailableProviders();
    const items = providers.map(p => ({
        label: p.name,
        description: p.available ? 'Available' : 'Not available'
    }));

    const selected = await vscode.window.showQuickPick(items, {
        placeHolder: 'Select AI model'
    });

    if (selected) {
        await vscode.workspace.getConfiguration().update('ai-augment.provider', selected.label, true);
        vscode.window.showInformationMessage(`Selected: ${selected.label}`);
    }
}

async function getAvailableProviders(): Promise<AIProvider[]> {
    const providers: AIProvider[] = [];
    
    // Check Ollama
    try {
        await axios.get(`${OLLAMA_HOST}/api/tags`);
        providers.push({ name: 'Ollama (Offline)', available: true });
    } catch {
        providers.push({ name: 'Ollama (Offline)', available: false });
    }

    // Check OpenAI
    const apiKey = vscode.workspace.getConfiguration().get('ai-augment.openaiKey');
    providers.push({ 
        name: 'OpenAI (Online)', 
        available: !!apiKey 
    });

    return providers;
}

async function callAI(prompt: string): Promise<string> {
    const provider = vscode.workspace.getConfiguration().get('ai-augment.provider', 'Ollama (Offline)');
    
    if (provider.includes('OpenAI')) {
        return callOpenAI(prompt);
    } else {
        return callOllama(prompt);
    }
}

async function callOpenAI(prompt: string): Promise<string> {
    const apiKey = vscode.workspace.getConfiguration().get('ai-augment.openaiKey');
    if (!apiKey) {
        throw new Error('OpenAI API key not configured');
    }

    const response = await axios.post(OPENAI_API_URL, {
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: prompt }],
        max_tokens: 1000
    }, {
        headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
        }
    });

    return response.data.choices[0].message.content;
}

async function callOllama(prompt: string, model = 'llama3'): Promise<string> {
    const response = await axios.post(`${OLLAMA_HOST}/api/generate`, {
        model,
        prompt,
        stream: false
    });
    return response.data.response;
}

async function explainCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const language = editor.document.languageId;
    const prompt = `Explain this ${language} code in simple terms for a beginner:\n\n${selectedText}\n\nExplanation:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is analyzing your code...",
        cancellable: false
    }, async () => {
        try {
            const explanation = await callAI(prompt);
            showResult('Code Explanation', explanation);
        } catch (error) {
            vscode.window.showErrorMessage('Failed to get explanation. Check your AI provider settings.');
        }
    });
}

async function refactorCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    if (!selectedText) return;

    const language = editor.document.languageId;
    const prompt = `Refactor this ${language} code to make it cleaner and more efficient. Only return the improved code:\n\n${selectedText}`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is refactoring your code...",
        cancellable: false
    }, async () => {
        try {
            const refactoredCode = await callAI(prompt);
            editor.edit(editBuilder => {
                editBuilder.replace(selection, refactoredCode.trim());
            });
        } catch (error) {
            vscode.window.showErrorMessage('Failed to refactor code');
        }
    });
}

async function generateCode() {
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe what you want to create',
        placeHolder: 'e.g., function to sort array, class for user management'
    });

    if (!prompt) return;

    const language = await vscode.window.showQuickPick([
        'JavaScript', 'Python', 'Java', 'C++', 'C#', 'TypeScript', 'Go', 'Rust'
    ], { placeHolder: 'Select programming language' });

    if (!language) return;

    const fullPrompt = `Generate ${language} code for: ${prompt}\n\nOnly return the code:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is generating code...",
        cancellable: false
    }, async () => {
        try {
            const generatedCode = await callAI(fullPrompt);
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                editor.edit(editBuilder => {
                    editBuilder.insert(editor.selection.active, generatedCode.trim());
                });
            } else {
                const doc = await vscode.workspace.openTextDocument({
                    content: generatedCode.trim(),
                    language: language.toLowerCase()
                });
                vscode.window.showTextDocument(doc);
            }
        } catch (error) {
            vscode.window.showErrorMessage('Failed to generate code');
        }
    });
}

async function documentCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) return;

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    if (!selectedText) return;

    const language = editor.document.languageId;
    const prompt = `Add proper documentation/comments to this ${language} code. Return the code with comments:\n\n${selectedText}`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is documenting your code...",
        cancellable: false
    }, async () => {
        try {
            const documentation = await callAI(prompt);
            editor.edit(editBuilder => {
                editBuilder.replace(selection, documentation.trim());
            });
        } catch (error) {
            vscode.window.showErrorMessage('Failed to generate documentation');
        }
    });
}

function showResult(title: string, content: string) {
    const panel = vscode.window.createWebviewPanel(
        'aiResult',
        title,
        vscode.ViewColumn.Beside,
        {}
    );
    panel.webview.html = getWebviewContent(content);
}

function getWebviewContent(text: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Result</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
            line-height: 1.6;
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
        }
        pre {
            background-color: var(--vscode-textBlockQuote-background);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            border-left: 4px solid var(--vscode-textBlockQuote-border);
        }
        code {
            background-color: var(--vscode-textPreformat-background);
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div>${text.replace(/\n/g, '<br>').replace(/```(.*?)```/gs, '<pre><code>$1</code></pre>')}</div>
</body>
</html>`;
}

export function deactivate() {}
