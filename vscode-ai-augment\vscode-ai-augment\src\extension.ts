import * as vscode from 'vscode';
import axios from 'axios';

const OLLAMA_HOST = 'http://localhost:11434';

export function activate(context: vscode.ExtensionContext) {
    console.log('AI Augment extension activated');

    // Register commands
    const explainCommand = vscode.commands.registerCommand('ai-augment.explain', explainCode);
    const refactorCommand = vscode.commands.registerCommand('ai-augment.refactor', refactorCode);
    const generateCommand = vscode.commands.registerCommand('ai-augment.generate', generateCode);
    const documentCommand = vscode.commands.registerCommand('ai-augment.document', documentCode);

    context.subscriptions.push(explainCommand, refactorCommand, generateCommand, documentCommand);
}

async function callOllama(prompt: string, model = 'llama3'): Promise<string> {
    try {
        const response = await axios.post(`${OLLAMA_HOST}/api/generate`, {
            model,
            prompt,
            stream: false
        });

        return response.data.response;
    } catch (error) {
        console.error('Error calling Ollama:', error);
        throw error;
    }
}

async function explainCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const prompt = `Explain the following code in simple terms:\n\n${selectedText}\n\nExplanation:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is analyzing your code...",
        cancellable: false
    }, async () => {
        try {
            const explanation = await callOllama(prompt);
            const panel = vscode.window.createWebviewPanel(
                'codeExplanation',
                'Code Explanation',
                vscode.ViewColumn.Beside,
                {}
            );
            panel.webview.html = getWebviewContent(explanation);
        } catch (error) {
            vscode.window.showErrorMessage('Failed to get explanation');
        }
    });
}

async function refactorCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const prompt = `Refactor the following code to make it more efficient and readable:\n\n${selectedText}\n\nRefactored code:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is refactoring your code...",
        cancellable: false
    }, async () => {
        try {
            const refactoredCode = await callOllama(prompt);
            editor.edit(editBuilder => {
                editBuilder.replace(selection, refactoredCode);
            });
        } catch (error) {
            vscode.window.showErrorMessage('Failed to refactor code');
        }
    });
}

async function generateCode() {
    const prompt = await vscode.window.showInputBox({
        prompt: 'Describe the code you want to generate',
        placeHolder: 'e.g., A Python function to calculate factorial'
    });

    if (!prompt) {
        return;
    }

    const fullPrompt = `Generate code based on the following description:\n\n${prompt}\n\nCode:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is generating code...",
        cancellable: false
    }, async () => {
        try {
            const generatedCode = await callOllama(fullPrompt);
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                editor.edit(editBuilder => {
                    editBuilder.insert(editor.selection.active, generatedCode);
                });
            } else {
                const doc = await vscode.workspace.openTextDocument({
                    content: generatedCode,
                    language: 'python' // Default language
                });
                vscode.window.showTextDocument(doc);
            }
        } catch (error) {
            vscode.window.showErrorMessage('Failed to generate code');
        }
    });
}

async function documentCode() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showErrorMessage('No active editor');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);

    if (!selectedText) {
        vscode.window.showErrorMessage('No code selected');
        return;
    }

    const prompt = `Generate documentation for the following code:\n\n${selectedText}\n\nDocumentation:`;
    
    vscode.window.withProgress({
        location: vscode.ProgressLocation.Notification,
        title: "AI is documenting your code...",
        cancellable: false
    }, async () => {
        try {
            const documentation = await callOllama(prompt);
            editor.edit(editBuilder => {
                // Insert documentation above the selected code
                const position = new vscode.Position(selection.start.line, 0);
                editBuilder.insert(position, documentation + '\n\n');
            });
        } catch (error) {
            vscode.window.showErrorMessage('Failed to generate documentation');
        }
    });
}

function getWebviewContent(text: string): string {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Explanation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div>${text.replace(/\n/g, '<br>')}</div>
</body>
</html>`;
}

export function deactivate() {}
